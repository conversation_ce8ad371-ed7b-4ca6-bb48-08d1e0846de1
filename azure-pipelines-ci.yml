name: Terraform-CI-Pipeline

trigger:
  branches:
    include:
      - main

pr:
  branches:
    include:
      - main

pool:
  vmImage: 'ubuntu-latest'

variables:
  - group: terraform-var-group
  - name: environment
    value: dev
  - name: TF_VERSION
    value: '1.6.1'
  - name: WORKING_DIR
    value: '$(System.DefaultWorkingDirectory)'

stages:
- stage: DetectChanges
  jobs:
  - job: IdentifyChangedWorkspaces
    steps:
    - checkout: self
      fetchDepth: 0

    - bash: |
        # Get changed files between the last two commits
        changed_files=$(git diff --name-only HEAD^ HEAD)

        # Use an associative array to track unique environment/workspace combinations.
        declare -A workspace_set

        # Flag to indicate if any workspace is affected
        has_workspace='false'

        # Process changed files to detect direct workspace changes.
        # This will match paths like: environments/<env>/workspaces/<workspace>/...
        while IFS= read -r file; do
          if [[ "$file" =~ ^environments/([^/]+)/workspaces/([^/]+)/ ]]; then
            env="${BASH_REMATCH[1]}"
            ws="${BASH_REMATCH[2]}"
            # Check if the workspace directory actually exists
            if [ -d "environments/$env/workspaces/$ws" ]; then
              key="${env}/${ws}"
              # Insert into associative array; duplicate keys will be overwritten
              workspace_set["$key"]="$env:$ws"
              has_workspace='true'
            else
              echo "Warning: Referenced workspace $ws does not exist, skipping."
            fi
          fi
        done <<< "$changed_files"

        # If any file under modules/ changed, check all workspaces for module references.
        if echo "$changed_files" | grep -q "^modules/"; then
          # Loop through each environment folder
          for env_dir in environments/*; do
            if [ -d "$env_dir" ]; then
              # Loop through each workspace within the environment
              for ws_dir in "$env_dir"/workspaces/*; do
                if [ -d "$ws_dir" ] && [ -f "$ws_dir/main.tf" ]; then
                  # If the workspace's main.tf references a module from modules/
                  if grep -q "module.*source.*modules/" "$ws_dir/main.tf"; then
                    env=$(basename "$env_dir")
                    ws=$(basename "$ws_dir")
                    # Double-check that the workspace directory exists (should always be true here, but being cautious)
                    if [ -d "environments/$env/workspaces/$ws" ]; then
                      key="${env}/${ws}"
                      workspace_set["$key"]="$env:$ws"
                      has_workspace='true'
                    else
                      echo "Warning: Workspace directory $ws unexpectedly does not exist, skipping."
                    fi
                  fi
                fi
              done
            fi
          done
        fi

        # Build the JSON object using only the unique keys from the associative array.
        json_object="{"
        first=true
        KEYS=$(echo "${!workspace_set[@]}" |  tr ' ' '\012' | sort | tr '\012' ' ')
        for key in ${KEYS}; do
          if [ "$first" = true ]; then
            first=false
          else
            json_object="$json_object,"
          fi
          # Split the stored value (format "env:workspace") into separate variables.
          IFS=":" read env ws <<< "${workspace_set[$key]}"
          json_object="$json_object\"$key\":{\"environment\":\"$env\",\"workspace\":\"$ws\"}"
        done
        json_object="$json_object}"

        # Set Azure DevOps variables using logging commands.
        echo "##vso[task.setvariable variable=HAS_WORKSPACE;isOutput=true]$has_workspace"
        echo "##vso[task.setvariable variable=WORKSPACE_MATRIX;isOutput=true]$json_object"

        # Log the JSON and flag.
        echo "$json_object"
        echo "Has workspace: $has_workspace"
      name: detectChanges

- stage: Validate
  dependsOn: DetectChanges
  condition: eq(dependencies.DetectChanges.outputs['IdentifyChangedWorkspaces.detectChanges.HAS_WORKSPACE'], 'true')
  jobs:
  - job: Validate_Workspaces
    variables:
      - name: matrix
        value: $[ stageDependencies.DetectChanges.IdentifyChangedWorkspaces.outputs['detectChanges.WORKSPACE_MATRIX'] ]
    strategy:
      matrix: ${{ variables.matrix }}
    steps:
      - checkout: self

      - bash: |
          echo "Environment: $(environment)"
          echo "Workspace: $(workspace)"
        displayName: 'Print Environment and Workspace'

      - bash: |
          if [ ! -d "environments/$(environment)/workspaces/$(workspace)" ]; then
            echo "##vso[task.logissue type=error]Workspace $(workspace) does not exist in environment $(environment)."
            echo "##vso[task.complete result=Failed;]"
          else
            echo "Workspace $(workspace) exists in environment $(environment). Proceeding with validation."
          fi
        displayName: 'Validate Workspace Exists'
        condition: succeeded()

      - task: UsePythonVersion@0
        inputs:
          versionSpec: '3.x'
          addToPath: true

      - script: |
          # Install Terraform
          curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo apt-key add -
          sudo apt-add-repository "deb https://apt.releases.hashicorp.com $(lsb_release -cs) main"
          sudo apt-get update && sudo apt-get install terraform
          terraform --version
        displayName: 'Install Terraform'

      - script: |
          curl -s https://raw.githubusercontent.com/terraform-linters/tflint/master/install_linux.sh | bash
        displayName: 'Install TFLint'

      # - task: TerraformInstaller@0
      #   inputs:
      #     terraformVersion: $(TF_VERSION)

      - script: |
          tflint --init && \
          tflint --config $(WORKING_DIR)/.tflint.hcl --chdir=$(WORKING_DIR)/environments/$(environment)/workspaces/$(workspace) || exit 1
        displayName: 'Run TFLint'

      - script: |
          pip install pre-commit
          pre-commit run --config .pre-commit-config.yml --files environments/$(environment)/workspaces/$(workspace)/*
        displayName: 'Run pre-commit'
        continueOnError: false

      - script: |
          AZURE_VARS="export ARM_CLIENT_ID=$(ARM_CLIENT_ID) && \
                 export ARM_CLIENT_SECRET=$(ARM_CLIENT_SECRET) && \
                 export ARM_SUBSCRIPTION_ID=$(ARM_SUBSCRIPTION_ID) && \
                 export ARM_TENANT_ID=$(ARM_TENANT_ID)"

      - script: |
          cd $(WORKING_DIR)/environments/$(environment)/workspaces/$(workspace) && \
          $AZURE_VARS && \
          terraform init \
            -backend-config="resource_group_name=ar-az-est1-tfstate-rg" \
            -backend-config="storage_account_name=arazest1tfstate$(environment)" \
            -backend-config="container_name=tfstate" \
            -backend-config="key=terraform.tfstate" \
            -reconfigure
        displayName: 'Terraform Init'
        env:
          ARM_CLIENT_ID: $(ARM_CLIENT_ID)
          ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
          ARM_SUBSCRIPTION_ID: $(ARM_SUBSCRIPTION_ID)
          ARM_TENANT_ID: $(ARM_TENANT_ID)

      - script: |
          cd $(WORKING_DIR)/environments/$(environment)/workspaces/$(workspace) && \
          $AZURE_VARS && \
          terraform workspace select $(workspace) || terraform workspace new $(workspace)
        displayName: 'Terraform Select Workspace'
        env:
          ARM_CLIENT_ID: $(ARM_CLIENT_ID)
          ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
          ARM_SUBSCRIPTION_ID: $(ARM_SUBSCRIPTION_ID)
          ARM_TENANT_ID: $(ARM_TENANT_ID)

      - script: |
          cd $(WORKING_DIR)/environments/$(environment)/workspaces/$(workspace) && \
          terraform validate
        displayName: 'Terraform Validate'

      - script: |
          cd $(WORKING_DIR)/environments/$(environment)/workspaces/$(workspace) && \
          $AZURE_VARS && \
          terraform plan \
            -out=$(workspace)-plan
        displayName: 'Terraform Plan'
        env:
          ARM_CLIENT_ID: $(ARM_CLIENT_ID)
          ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
          ARM_SUBSCRIPTION_ID: $(ARM_SUBSCRIPTION_ID)
          ARM_TENANT_ID: $(ARM_TENANT_ID)

      # - task: TerraformTaskV3@3
      #   name: terraformInit
      #   inputs:
      #     provider: 'azurerm'
      #     command: 'init'
      #     workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
      #     backendServiceArm: '$(environment)-service-connection'
      #     backendAzureRmResourceGroupName: 'ar-az-est1-tfstate-rg'
      #     backendAzureRmStorageAccountName: 'arazest1tfstate$(environment)'
      #     backendAzureRmContainerName: 'tfstate'
      #     backendAzureRmKey: 'terraform.tfstate'

      # - task: TerraformTaskV3@3
      #   name: terraformSelectWorkspace
      #   inputs:
      #     provider: 'azurerm'
      #     command: 'custom'
      #     customCommand: 'workspace'
      #     commandOptions: 'select $(workspace)'
      #     workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
      #     environmentServiceNameAzureRM: '$(environment)-service-connection'

      # - task: TerraformTaskV3@3
      #   name: terraformValidate
      #   inputs:
      #     provider: 'azurerm'
      #     command: 'validate'
      #     workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
      #     environmentServiceNameAzureRM: '$(environment)-service-connection'

      # - task: TerraformTaskV3@3
      #   name: terraformPlan
      #   inputs:
      #     provider: 'azurerm'
      #     command: 'plan'
      #     workingDirectory: 'environments/$(environment)/workspaces/$(workspace)'
      #     environmentServiceNameAzureRM: '$(environment)-service-connection'
      #     publishPlanResults: '$(workspace)-plan'


- stage: Approval
  displayName: 'Manual Approval'
  pool: server
  condition: always()
  jobs:
  - job: WaitForApproval
    steps:
    - task: ManualValidation@0
      inputs:
        notifyUsers: |
          <EMAIL>
        instructions: 'Please validate the merge for $(environment) before proceeding with the deployment.'
        onTimeout: 'reject'