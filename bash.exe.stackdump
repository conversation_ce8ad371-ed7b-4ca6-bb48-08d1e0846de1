Stack trace:
Frame         Function      Args
0007FFFF5800  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF4700) msys-2.0.dll+0x1FEBA
0007FFFF5800  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF5AD8) msys-2.0.dll+0x67F9
0007FFFF5800  000210046832 (000210285FF9, 0007FFFF56B8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF5800  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF5800  0002100690B4 (0007FFFF5810, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF5AE0  00021006A49D (0007FFFF5810, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF2B520000 ntdll.dll
7FFF2A680000 KERNEL32.DLL
7FFF28670000 KERNELBASE.dll
7FFF29510000 USER32.dll
7FFF28DC0000 win32u.dll
7FFF2A750000 GDI32.dll
7FFF29000000 gdi32full.dll
7FFF28DF0000 msvcp_win.dll
7FFF28BE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF2B3A0000 advapi32.dll
7FFF29330000 msvcrt.dll
7FFF293E0000 sechost.dll
7FFF29BC0000 RPCRT4.dll
7FFF27B80000 CRYPTBASE.DLL
7FFF28EA0000 bcryptPrimitives.dll
7FFF2A820000 IMM32.DLL
